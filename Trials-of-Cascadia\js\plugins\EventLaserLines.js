//=============================================================================
// EventLaserLines.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc Animated laser beams from events via notetags/comments with glow, pulse, flicker, arcs. v1.0.0
 * <AUTHOR> Agent
 *
 * @param defaultColor
 * @text Default Color
 * @type string
 * @default #FF0000
 *
 * @param defaultWidth
 * @text Default Width (px)
 * @type number
 * @min 1
 * @max 20
 * @default 4
 *
 * @param defaultOpacity
 * @text Default Opacity (0-255)
 * @type number
 * @min 0
 * @max 255
 * @default 220
 *
 * @param pulseSpeed
 * @text Pulse Speed
 * @type number
 * @decimals 2
 * @min 0.01
 * @max 1.00
 * @default 0.10
 *
 * @param floatAmplitude
 * @text Float Amplitude (px)
 * @type number
 * @min 0
 * @max 10
 * @default 1.5
 *
 * @param floatSpeed
 * @text Float Speed
 * @type number
 * @decimals 2
 * @min 0.01
 * @max 1.00
 * @default 0.05
 *
 * @param enableGlow
 * @text Enable Glow Globally
 * @type boolean
 * @default true
 *
 * @param enableElectricArcs
 * @text Enable Electric Arcs Globally
 * @type boolean
 * @default true
 *
 * @param enableWarningPulse
 * @text Enable Warning Pulse Globally
 * @type boolean
 * @default false
 *
 * @param arcFrequency
 * @text Arc Frequency (frames)
 * @type number
 * @min 1
 * @max 120
 * @default 18
 *
 * @param flickerIntensity
 * @text Flicker Intensity (0-1)
 * @type number
 * @decimals 2
 * @min 0
 * @max 1
 * @default 0.40
 *
 * @help
 * Notetags (event note or current page comments):
 * <laser: direction=right, length=150, color=#FF0000, width=4, opacity=220, pulse=true, flicker=false, glow=true, glowSize=16, electricArcs=true, warningPulse=false, float=true>
 * Multiple <laser: ...> tags are supported per event.
 *
 * Directions: right, up, left, down
 * Ranges: length 10-1000, width 1-20, glowSize 0-50, opacity 0-255
 * Booleans: true/false (case-insensitive)
 *
 * Layering: Lasers render on a dedicated layer below characters and above tiles.
 * Cleanup: Lasers are destroyed on scene change and map refresh.
 *
 * This is an initial version optimized for performance with cached bitmaps and
 * minimal per-frame math; animations use a sine lookup table.
 *
 * @license MIT
 */
(() => {
  'use strict';

  const PLUGIN_NAME = 'EventLaserLines';
  const P = PluginManager.parameters(PLUGIN_NAME);
  const DEFAULTS = {
    color: String(P['defaultColor'] || '#FF0000'),
    width: clampNum(Number(P['defaultWidth'] || 4), 1, 20),
    opacity: clampNum(Number(P['defaultOpacity'] || 220), 0, 255),
    pulseSpeed: Number(P['pulseSpeed'] || 0.1),
    floatAmp: Number(P['floatAmplitude'] || 1.5),
    floatSpeed: Number(P['floatSpeed'] || 0.05),
    enableGlow: P['enableGlow'] === 'true',
    enableElectricArcs: P['enableElectricArcs'] === 'true',
    enableWarningPulse: P['enableWarningPulse'] === 'true',
    arcFrequency: clampNum(Number(P['arcFrequency'] || 18), 1, 120),
    flickerIntensity: clampNum(Number(P['flickerIntensity'] || 0.4), 0, 1),
  };

  // Sine lookup table for 0..359 deg
  const SINE_TABLE = (() => {
    const t = new Array(360);
    for (let i = 0; i < 360; i++) t[i] = Math.sin((i * Math.PI) / 180);
    return t;
  })();
  function sinFast(rad) {
    const deg = (rad * 180 / Math.PI) % 360;
    const idx = (deg < 0 ? deg + 360 : deg) | 0;
    return SINE_TABLE[idx];
  }

  function clampNum(n, min, max) { return isNaN(n) ? min : Math.max(min, Math.min(max, n)); }
  function parseBool(v, def) {
    if (v == null) return !!def;
    const s = String(v).trim().toLowerCase();
    if (s === 'true' || s === '1' || s === 'yes' || s === 'y') return true;
    if (s === 'false' || s === '0' || s === 'no' || s === 'n') return false;
    return !!def;
  }
  function parseColorHex(s, def) {
    if (!s || typeof s !== 'string') return def || '#FFFFFF';
    const m = s.trim().match(/^#?[0-9a-fA-F]{6}$/);
    return m ? (s[0] === '#' ? s : '#' + s) : (def || '#FFFFFF');
  }

  function eventActivePageComments(gameEvent) {
    // Concatenate current page comment lines (code 108/408)
    try {
      const list = gameEvent.list && gameEvent.list();
      if (!list) return '';
      let out = '';
      for (const cmd of list) {
        if (cmd && (cmd.code === 108 || cmd.code === 408) && cmd.parameters && cmd.parameters[0]) {
          out += cmd.parameters[0] + '\n';
        }
      }
      return out;
    } catch (e) {
      return '';
    }
  }

  function parseLaserNotetagsFromText(text) {
    const out = [];
    if (!text) return out;
    const re = /<laser\s*:(.*?)>/gi;
    let m;
    while ((m = re.exec(text)) !== null) {
      const paramStr = (m[1] || '').trim();
      const params = parseLaserParams(paramStr);
      if (params) out.push(params);
    }
    return out;
  }

  function parseLaserParams(paramStr) {
    try {
      const getKV = (rx, mapFn) => {
        const mm = paramStr.match(rx);
        return mm ? mapFn(mm[1]) : undefined;
      };
      const direction = (getKV(/direction=([a-zA-Z]+)/i, s => s.toLowerCase()) || 'right');
      const length = clampNum(getKV(/length=(\d+)/i, Number) ?? 150, 10, 1000);
      const color = parseColorHex(getKV(/color=#?([0-9a-fA-F]{6})/i, s => '#' + s) || DEFAULTS.color, DEFAULTS.color);
      const width = clampNum(getKV(/width=(\d+)/i, Number) ?? DEFAULTS.width, 1, 20);
      const opacity = clampNum(getKV(/opacity=(\d+)/i, Number) ?? DEFAULTS.opacity, 0, 255);
      const pulse = parseBool(getKV(/pulse=([a-zA-Z0-9]+)/i, s => s), false);
      const flicker = parseBool(getKV(/flicker=([a-zA-Z0-9]+)/i, s => s), false);
      const glow = parseBool(getKV(/glow=([a-zA-Z0-9]+)/i, s => s), DEFAULTS.enableGlow);
      const glowSize = clampNum(getKV(/glowSize=(\d+)/i, Number) ?? 12, 0, 50);
      const electricArcs = parseBool(getKV(/electricArcs=([a-zA-Z0-9]+)/i, s => s), DEFAULTS.enableElectricArcs);
      const warningPulse = parseBool(getKV(/warningPulse=([a-zA-Z0-9]+)/i, s => s), DEFAULTS.enableWarningPulse);
      const floaty = parseBool(getKV(/float=([a-zA-Z0-9]+)/i, s => s), false);
      return { direction, length, color, width, opacity, pulse, flicker, glow, glowSize, electricArcs, warningPulse, floaty };
    } catch (e) {
      console.warn('EventLaserLines: bad params "' + paramStr + '"', e);
      return null;
    }
  }

  class LaserLine extends Sprite {
    constructor(settings) {
      super();
      this.settings = settings;
      this._time = 0;
      this._baseOpacity = settings.opacity;
      this._floatPhase = Math.random() * Math.PI * 2;
      this._arcTimer = 0;
      this._destroyed = false;
      this.anchor.set(0, 0.5); // default: extend right from left edge
      this.blendMode = PIXI.BLEND_MODES.ADD;
      this._createBeamBitmap();
      this._createOverlays();
      this._applyDirection();
    }
    _applyDirection() {
      const d = this.settings.direction;
      // Anchor so event position is the emitter origin
      if (d === 'right') { this.rotation = 0; this.anchor.set(0, 0.5); }
      else if (d === 'left') { this.rotation = Math.PI; this.anchor.set(0, 0.5); }
      else if (d === 'down') { this.rotation = Math.PI / 2; this.anchor.set(0, 0.5); }
      else if (d === 'up') { this.rotation = -Math.PI / 2; this.anchor.set(0, 0.5); }
      else { this.rotation = 0; this.anchor.set(0, 0.5); }
    }
    _createBeamBitmap() {
      const len = this.settings.length;
      const w = Math.max(1, this.settings.width);
      const glow = this.settings.glow ? this.settings.glowSize : 0;
      const H = Math.max(1, w + glow * 2);
      const W = Math.max(2, len + glow * 2);
      const bmp = new Bitmap(W, H);
      const ctx = bmp.context;
      const { r, g, b } = hexToRgb(this.settings.color);
      ctx.save();
      // Outer atmospheric glow
      if (glow > 0) {
        const gradOuter = ctx.createLinearGradient(0, 0, 0, H);
        gradOuter.addColorStop(0, `rgba(${r},${g},${b},0)`);
        gradOuter.addColorStop(0.5, `rgba(${r},${g},${b},0.18)`);
        gradOuter.addColorStop(1, `rgba(${r},${g},${b},0)`);
        ctx.fillStyle = gradOuter;
        ctx.fillRect(0, 0, W, H);
      }
      // Medium scattering
      if (glow > 6) {
        const pad = Math.max(0, (glow) * 0.5);
        const h2 = Math.max(1, w + pad);
        const y2 = (H - h2) / 2;
        const gradMed = ctx.createLinearGradient(0, y2, 0, y2 + h2);
        gradMed.addColorStop(0, `rgba(${r},${g},${b},0)`);
        gradMed.addColorStop(0.5, `rgba(${r},${g},${b},0.35)`);
        gradMed.addColorStop(1, `rgba(${r},${g},${b},0)`);
        ctx.fillStyle = gradMed;
        ctx.fillRect(0, y2, W, h2);
      }
      // Inner glow
      {
        const h3 = Math.max(1, w + 2);
        const y3 = (H - h3) / 2;
        const gradInner = ctx.createLinearGradient(0, y3, 0, y3 + h3);
        gradInner.addColorStop(0, `rgba(${r},${g},${b},0)`);
        gradInner.addColorStop(0.5, `rgba(${r},${g},${b},0.7)`);
        gradInner.addColorStop(1, `rgba(${r},${g},${b},0)`);
        ctx.fillStyle = gradInner;
        ctx.fillRect(0, y3, W, h3);
      }
      // Core beam
      {
        const h4 = Math.max(1, w);
        const y4 = (H - h4) / 2;
        ctx.fillStyle = `rgba(${r},${g},${b},0.95)`;
        ctx.fillRect(glow, y4, len, h4);
      }
      // White-hot center core
      {
        const h5 = Math.max(1, Math.round(Math.max(1, w * 0.35)));
        const y5 = (H - h5) / 2;
        ctx.fillStyle = `rgba(255,255,255,0.85)`;
        ctx.fillRect(glow, y5, len, h5);
      }
      ctx.restore();
      if (bmp && bmp._setDirty) bmp._setDirty();
      this.bitmap = bmp;
      // Positioning reference: left edge at origin (anchor.x = 0)
      this.x = 0; this.y = 0;
      this.opacity = this.settings.opacity;
    }
    _createOverlays() {
      // Electric arcs overlay
      if (this.settings.electricArcs) {
        const W = this.bitmap.width, H = this.bitmap.height;
        this._arcSprite = new Sprite(new Bitmap(W, H));
        this._arcSprite.anchor.set(this.anchor.x, this.anchor.y);
        this._arcSprite.blendMode = PIXI.BLEND_MODES.ADD;
        this._arcSprite.opacity = 0;
        this.addChild(this._arcSprite);
      }
      // Warning pulse overlay
      if (this.settings.warningPulse) {
        const W = this.bitmap.width, H = this.bitmap.height;
        this._warnSprite = new Sprite(new Bitmap(W, H));
        const ctx = this._warnSprite.bitmap.context;
        ctx.fillStyle = 'rgba(255,0,0,0.2)';
        ctx.fillRect(0, 0, W, H);
        this._warnSprite.anchor.set(this.anchor.x, this.anchor.y);
        this._warnSprite.blendMode = PIXI.BLEND_MODES.ADD;
        this._warnSprite.opacity = 0;
        this.addChild(this._warnSprite);
      }
    }
    _drawArcs() {
      if (!this._arcSprite) return;
      const b = this._arcSprite.bitmap;
      b.clear();
      const ctx = b.context;
      const { r, g, b: bb } = hexToRgb(this.settings.color);
      // Create 1-2 arcs spanning random segment of the length
      const arcs = 1 + (Math.random() < 0.5 ? 1 : 0);
      for (let a = 0; a < arcs; a++) {
        const sx = Math.floor(this.settings.glow ? this.settings.glowSize : 0) + Math.floor(Math.random() * (this.settings.length * 0.5));
        const ex = sx + Math.floor(this.settings.length * (0.3 + Math.random() * 0.4));
        const cy = b.height / 2;
        const segs = 4 + Math.floor(Math.random() * 4);
        ctx.lineCap = 'round';
        // white core
        ctx.strokeStyle = 'rgba(255,255,255,0.9)';
        ctx.lineWidth = 1.2;
        ctx.beginPath();
        for (let i = 0; i <= segs; i++) {
          const t = i / segs;
          const x = sx + (ex - sx) * t;
          const amp = (this.settings.width + 2) * 0.4;
          const y = cy + (Math.random() - 0.5) * amp;
          if (i === 0) ctx.moveTo(x, y); else ctx.lineTo(x, y);
        }
        ctx.stroke();
        // colored glow pass
        ctx.strokeStyle = `rgba(${r},${g},${bb},0.55)`;
        ctx.lineWidth = 2.2;
        ctx.stroke();
      }
      this._arcSprite.opacity = 200;
      if (b && b._setDirty) b._setDirty();
    }
    update() {
      if (this._destroyed) return false;
      super.update();
      this._time += 1;
      // Pulse
      if (this.settings.pulse) {
        const sp = DEFAULTS.pulseSpeed;
        const a = 0.85 + 0.15 * sinFast(this._time * sp);
        this.opacity = clampNum(Math.round(this._baseOpacity * a), 0, 255);
      } else {
        this.opacity = this._baseOpacity;
      }
      // Flicker (multiplicative occasional dips)
      if (this.settings.flicker && Math.random() < 0.12) {
        this.opacity = Math.round(this.opacity * (1 - DEFAULTS.flickerIntensity * Math.random()));
      }
      // Warning pulse overlay
      if (this._warnSprite) {
        const v = 0.5 + 0.5 * sinFast(this._time * 0.2);
        this._warnSprite.opacity = Math.round(255 * 0.35 * v);
      }
      // Electric arcs timing
      if (this._arcSprite) {
        this._arcTimer--;
        if (this._arcTimer <= 0) {
          this._drawArcs();
          this._arcTimer = DEFAULTS.arcFrequency + Math.floor(Math.random() * 8);
        } else {
          // fade out
          this._arcSprite.opacity = Math.max(0, this._arcSprite.opacity - 25);
        }
      }
      // Floaty offset perpendicular to beam
      if (this.settings.floaty) {
        const phase = this._floatPhase + this._time * DEFAULTS.floatSpeed;
        const off = DEFAULTS.floatAmp * sinFast(phase);
        // Apply perpendicular offset by using rotation +/- 90deg
        const nx = Math.cos(this.rotation + Math.PI / 2);
        const ny = Math.sin(this.rotation + Math.PI / 2);
        this._floatOffsetX = nx * off;
        this._floatOffsetY = ny * off;
      } else {
        this._floatOffsetX = 0; this._floatOffsetY = 0;
      }
      // Apply offset to children too
      if (this._arcSprite) { this._arcSprite.x = this._floatOffsetX; this._arcSprite.y = this._floatOffsetY; }
      if (this._warnSprite) { this._warnSprite.x = this._floatOffsetX; this._warnSprite.y = this._floatOffsetY; }
      return true;
    }
    destroy(options) {
      this._destroyed = true;
      try { if (this._arcSprite && this._arcSprite.bitmap) this._arcSprite.bitmap.destroy(); } catch(_){}
      try { if (this._warnSprite && this._warnSprite.bitmap) this._warnSprite.bitmap.destroy(); } catch(_){}
      try { if (this.bitmap) this.bitmap.destroy(); } catch(_){}
      super.destroy(options);
    }
  }

  function hexToRgb(hex) {
    const s = String(hex || '').replace('#', '').trim();
    const r = parseInt(s.substring(0, 2), 16);
    const g = parseInt(s.substring(2, 4), 16);
    const b = parseInt(s.substring(4, 6), 16);
    if (Number.isNaN(r) || Number.isNaN(g) || Number.isNaN(b)) {
      // Fallback to white if parsing fails; inputs are validated upstream.
      return { r: 255, g: 255, b: 255 };
    }
    return { r, g, b };
  }

  class LaserManager {
    constructor(scene) {
      this.scene = scene;
      this.layer = new Sprite();
      // Attach to the map tilemap so the engine's z-sorting applies.
      // Characters have z=3 (normal) or z=5 (above). Lower tiles are <=2.
      // Use 2.9 to keep lasers below characters but above lower tiles.
      this.layer.z = 2.9;
      if (scene._spriteset) {
        const ss = scene._spriteset;
        const container = ss._tilemap || ss._effectsContainer || ss;
        container.addChild(this.layer);
      }
      this.eventLasers = new Map(); // eventId -> { settingsList, sprites[] }
      this._lastMapId = $gameMap.mapId();

      // Performance optimization properties
      this._scanThrottleTimer = 0;
      this._pendingScans = new Set(); // Track which events need scanning
      this._isAsyncScanning = false;
      this._scanQueue = [];

      this.scanEventsForLasers();
    }
    terminate() {
      this.clearAll();
      if (this.layer && this.layer.parent) this.layer.parent.removeChild(this.layer);
    }
    clearAll() {
      this.eventLasers.forEach(entry => {
        if (entry && entry.sprites) entry.sprites.forEach(s => s && s.destroy());
      });
      this.eventLasers.clear();
    }
    scanEventsForLasers() {
      this.clearAll();
      $gameMap.events().forEach((ev) => {
        if (!ev || !ev.event()) return;
        const noteText = ev.event().note || '';
        const commentText = eventActivePageComments(ev) || '';
        const tags = [
          ...parseLaserNotetagsFromText(noteText),
          ...parseLaserNotetagsFromText(commentText)
        ];
        if (tags.length > 0) {
          const sprites = tags.map(t => new LaserLine(t));
          this.eventLasers.set(ev.eventId(), { settingsList: tags, sprites, evId: ev.eventId() });
          // Add to layer
          sprites.forEach(s => this.layer.addChild(s));
        }
      });
    }

    // SOLUTION 2: Selective Event Scanning - Only scan specific event
    scanSingleEventForLasers(eventId) {
      const ev = $gameMap.event(eventId);
      if (!ev || !ev.event()) return;

      // Remove existing lasers for this event
      const existing = this.eventLasers.get(eventId);
      if (existing && existing.sprites) {
        existing.sprites.forEach(s => {
          this.layer.removeChild(s);
          s.destroy();
        });
        this.eventLasers.delete(eventId);
      }

      // Scan for new lasers
      const noteText = ev.event().note || '';
      const commentText = eventActivePageComments(ev) || '';
      const tags = [
        ...parseLaserNotetagsFromText(noteText),
        ...parseLaserNotetagsFromText(commentText)
      ];

      if (tags.length > 0) {
        const sprites = tags.map(t => new LaserLine(t));
        this.eventLasers.set(eventId, { settingsList: tags, sprites, evId: eventId });
        sprites.forEach(s => this.layer.addChild(s));
      }
    }

    // SOLUTION 1: Throttled Full Scan with Delay
    scanEventsForLasersThrottled() {
      // Reset throttle timer
      this._scanThrottleTimer = 10; // 10 frame delay (~167ms at 60fps)
    }

    // SOLUTION 3: Async Processing - Process events in chunks
    async scanEventsForLasersAsync() {
      if (this._isAsyncScanning) return; // Prevent multiple async scans

      this._isAsyncScanning = true;
      this.clearAll();

      const events = $gameMap.events();
      const chunkSize = 5; // Process 5 events per frame

      for (let i = 0; i < events.length; i += chunkSize) {
        const chunk = events.slice(i, i + chunkSize);

        // Process chunk
        chunk.forEach((ev) => {
          if (!ev || !ev.event()) return;
          const noteText = ev.event().note || '';
          const commentText = eventActivePageComments(ev) || '';
          const tags = [
            ...parseLaserNotetagsFromText(noteText),
            ...parseLaserNotetagsFromText(commentText)
          ];
          if (tags.length > 0) {
            const sprites = tags.map(t => new LaserLine(t));
            this.eventLasers.set(ev.eventId(), { settingsList: tags, sprites, evId: ev.eventId() });
            sprites.forEach(s => this.layer.addChild(s));
          }
        });

        // Yield control back to the main thread
        await new Promise(resolve => setTimeout(resolve, 0));
      }

      this._isAsyncScanning = false;
    }
    update() {
      // Handle throttled scanning
      if (this._scanThrottleTimer > 0) {
        this._scanThrottleTimer--;
        if (this._scanThrottleTimer === 0) {
          // Execute delayed scan
          this.scanEventsForLasers();
        }
      }

      // Process pending selective scans
      if (this._pendingScans.size > 0 && this._scanThrottleTimer === 0) {
        const eventId = this._pendingScans.values().next().value;
        this._pendingScans.delete(eventId);
        this.scanSingleEventForLasers(eventId);
      }

      // Refresh if map changed
      if (this._lastMapId !== $gameMap.mapId()) {
        this._lastMapId = $gameMap.mapId();
        this._pendingScans.clear(); // Clear pending scans on map change
        this.scanEventsForLasersAsync(); // Use async scan for map changes
        return;
      }

      // Follow events and update
      this.eventLasers.forEach((entry) => {
        const ev = $gameMap.event(entry.evId);
        if (!ev) return;
        const spr = this.scene._spriteset && this.scene._spriteset.findTargetSprite ? this.scene._spriteset.findTargetSprite(ev) : null;
        if (!spr) return;
        const ox = spr.x, oy = spr.y;
        // Position all lasers at event origin, then apply float offsets inside sprite
        entry.sprites = entry.sprites.filter(s => {
          try {
            s.x = ox + (s._floatOffsetX || 0);
            s.y = oy + (s._floatOffsetY || 0);
            const alive = s.update();
            if (!alive) { this.layer.removeChild(s); s.destroy(); }
            return alive;
          } catch (e) {
            console.warn('EventLaserLines: sprite update error', e);
            try { this.layer.removeChild(s); s.destroy(); } catch(_){}
            return false;
          }
        });
      });
    }

    // Add method to queue selective event scanning
    queueEventScan(eventId) {
      this._pendingScans.add(eventId);
    }
  }

  // Scene_Map integration
  const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
  Scene_Map.prototype.createSpriteset = function() {
    _Scene_Map_createSpriteset.call(this);
    if (!this._laserManager) this._laserManager = new LaserManager(this);
  };
  const _Scene_Map_update = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function() {
    _Scene_Map_update.call(this);
    if (this._laserManager) this._laserManager.update();
  };
  const _Scene_Map_terminate = Scene_Map.prototype.terminate;
  Scene_Map.prototype.terminate = function() {
    if (this._laserManager) { this._laserManager.terminate(); this._laserManager = null; }
    _Scene_Map_terminate.call(this);
  };

  // Re-scan on Game_Map refresh
  const _Game_Map_refresh = Game_Map.prototype.refresh;
  Game_Map.prototype.refresh = function() {
    _Game_Map_refresh.call(this);
    if (SceneManager._scene && SceneManager._scene._laserManager) {
      SceneManager._scene._laserManager.scanEventsForLasers();
    }

  // Re-scan when an event's active page changes
  const _Game_Event_setupPage = Game_Event.prototype.setupPage;
  Game_Event.prototype.setupPage = function() {
    _Game_Event_setupPage.call(this);
    if (SceneManager._scene && SceneManager._scene._laserManager) {
      SceneManager._scene._laserManager.scanEventsForLasers();
    }
  };

  };
})();

