//=============================================================================
// EventLaserLines.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc Animated laser beams from events via notetags/comments with glow, pulse, flicker, arcs. v1.0.0
 * <AUTHOR> Agent
 *
 * @param defaultColor
 * @text Default Color
 * @type string
 * @default #FF0000
 *
 * @param defaultWidth
 * @text Default Width (px)
 * @type number
 * @min 1
 * @max 20
 * @default 4
 *
 * @param defaultOpacity
 * @text Default Opacity (0-255)
 * @type number
 * @min 0
 * @max 255
 * @default 220
 *
 * @param pulseSpeed
 * @text Pulse Speed
 * @type number
 * @decimals 2
 * @min 0.01
 * @max 1.00
 * @default 0.10
 *
 * @param floatAmplitude
 * @text Float Amplitude (px)
 * @type number
 * @min 0
 * @max 10
 * @default 1.5
 *
 * @param floatSpeed
 * @text Float Speed
 * @type number
 * @decimals 2
 * @min 0.01
 * @max 1.00
 * @default 0.05
 *
 * @param enableGlow
 * @text Enable Glow Globally
 * @type boolean
 * @default true
 *
 * @param enableElectricArcs
 * @text Enable Electric Arcs Globally
 * @type boolean
 * @default true
 *
 * @param enableWarningPulse
 * @text Enable Warning Pulse Globally
 * @type boolean
 * @default false
 *
 * @param arcFrequency
 * @text Arc Frequency (frames)
 * @type number
 * @min 1
 * @max 120
 * @default 18
 *
 * @param flickerIntensity
 * @text Flicker Intensity (0-1)
 * @type number
 * @decimals 2
 * @min 0
 * @max 1
 * @default 0.40
 *
 * @help
 * Notetags (event note or current page comments):
 * <laser: direction=right, length=150, color=#FF0000, width=4, opacity=220, pulse=true, flicker=false, glow=true, glowSize=16, electricArcs=true, warningPulse=false, float=true>
 * Multiple <laser: ...> tags are supported per event.
 *
 * Directions: right, up, left, down
 * Ranges: length 10-1000, width 1-20, glowSize 0-50, opacity 0-255
 * Booleans: true/false (case-insensitive)
 *
 * Layering: Lasers render on a dedicated layer below characters and above tiles.
 * Cleanup: Lasers are destroyed on scene change and map refresh.
 *
 * This is an initial version optimized for performance with cached bitmaps and
 * minimal per-frame math; animations use a sine lookup table.
 *
 * @license MIT
 */
(() => {
  'use strict';

  const PLUGIN_NAME = 'EventLaserLines';
  const P = PluginManager.parameters(PLUGIN_NAME);
  const DEFAULTS = {
    color: String(P['defaultColor'] || '#FF0000'),
    width: clampNum(Number(P['defaultWidth'] || 4), 1, 20),
    opacity: clampNum(Number(P['defaultOpacity'] || 220), 0, 255),
    pulseSpeed: Number(P['pulseSpeed'] || 0.1),
    floatAmp: Number(P['floatAmplitude'] || 1.5),
    floatSpeed: Number(P['floatSpeed'] || 0.05),
    enableGlow: P['enableGlow'] === 'true',
    enableElectricArcs: P['enableElectricArcs'] === 'true',
    enableWarningPulse: P['enableWarningPulse'] === 'true',
    arcFrequency: clampNum(Number(P['arcFrequency'] || 18), 1, 120),
    flickerIntensity: clampNum(Number(P['flickerIntensity'] || 0.4), 0, 1),
  };

  // Sine lookup table for 0..359 deg
  const SINE_TABLE = (() => {
    const t = new Array(360);
    for (let i = 0; i < 360; i++) t[i] = Math.sin((i * Math.PI) / 180);
    return t;
  })();
  function sinFast(rad) {
    const deg = (rad * 180 / Math.PI) % 360;
    const idx = (deg < 0 ? deg + 360 : deg) | 0;
    return SINE_TABLE[idx];
  }

  function clampNum(n, min, max) { return isNaN(n) ? min : Math.max(min, Math.min(max, n)); }
  function parseBool(v, def) {
    if (v == null) return !!def;
    const s = String(v).trim().toLowerCase();
    if (s === 'true' || s === '1' || s === 'yes' || s === 'y') return true;
    if (s === 'false' || s === '0' || s === 'no' || s === 'n') return false;
    return !!def;
  }
  function parseColorHex(s, def) {
    if (!s || typeof s !== 'string') return def || '#FFFFFF';
    const m = s.trim().match(/^#?[0-9a-fA-F]{6}$/);
    return m ? (s[0] === '#' ? s : '#' + s) : (def || '#FFFFFF');
  }

  function eventActivePageComments(gameEvent) {
    // Concatenate current page comment lines (code 108/408)
    try {
      const list = gameEvent.list && gameEvent.list();
      if (!list) return '';
      let out = '';
      for (const cmd of list) {
        if (cmd && (cmd.code === 108 || cmd.code === 408) && cmd.parameters && cmd.parameters[0]) {
          out += cmd.parameters[0] + '\n';
        }
      }
      return out;
    } catch (e) {
      return '';
    }
  }

  function parseLaserNotetagsFromText(text) {
    const out = [];
    if (!text) return out;
    const re = /<laser\s*:(.*?)>/gi;
    let m;
    while ((m = re.exec(text)) !== null) {
      const paramStr = (m[1] || '').trim();
      const params = parseLaserParams(paramStr);
      if (params) out.push(params);
    }
    return out;
  }

  function parseLaserParams(paramStr) {
    try {
      const getKV = (rx, mapFn) => {
        const mm = paramStr.match(rx);
        return mm ? mapFn(mm[1]) : undefined;
      };
      const direction = (getKV(/direction=([a-zA-Z]+)/i, s => s.toLowerCase()) || 'right');
      const length = clampNum(getKV(/length=(\d+)/i, Number) ?? 150, 10, 1000);
      const color = parseColorHex(getKV(/color=#?([0-9a-fA-F]{6})/i, s => '#' + s) || DEFAULTS.color, DEFAULTS.color);
      const width = clampNum(getKV(/width=(\d+)/i, Number) ?? DEFAULTS.width, 1, 20);
      const opacity = clampNum(getKV(/opacity=(\d+)/i, Number) ?? DEFAULTS.opacity, 0, 255);
      const pulse = parseBool(getKV(/pulse=([a-zA-Z0-9]+)/i, s => s), false);
      const flicker = parseBool(getKV(/flicker=([a-zA-Z0-9]+)/i, s => s), false);
      const glow = parseBool(getKV(/glow=([a-zA-Z0-9]+)/i, s => s), DEFAULTS.enableGlow);
      const glowSize = clampNum(getKV(/glowSize=(\d+)/i, Number) ?? 12, 0, 50);
      const electricArcs = parseBool(getKV(/electricArcs=([a-zA-Z0-9]+)/i, s => s), DEFAULTS.enableElectricArcs);
      const warningPulse = parseBool(getKV(/warningPulse=([a-zA-Z0-9]+)/i, s => s), DEFAULTS.enableWarningPulse);
      const floaty = parseBool(getKV(/float=([a-zA-Z0-9]+)/i, s => s), false);
      return { direction, length, color, width, opacity, pulse, flicker, glow, glowSize, electricArcs, warningPulse, floaty };
    } catch (e) {
      console.warn('EventLaserLines: bad params "' + paramStr + '"', e);
      return null;
    }
  }

  class LaserLine extends Sprite {
    constructor(settings) {
      super();
      this.setupForReuse(settings);
    }

    setupForReuse(settings) {
      this.settings = settings;
      this._time = 0;
      this._baseOpacity = settings.opacity;
      this._floatPhase = Math.random() * Math.PI * 2;
      this._arcTimer = 0;
      this._destroyed = false;
      this.anchor.set(0, 0.5); // default: extend right from left edge
      this.blendMode = PIXI.BLEND_MODES.ADD;
      this._createBeamBitmap();
      this._createOverlays();
      this._applyDirection();
    }
    _applyDirection() {
      const d = this.settings.direction;
      // Anchor so event position is the emitter origin
      if (d === 'right') { this.rotation = 0; this.anchor.set(0, 0.5); }
      else if (d === 'left') { this.rotation = Math.PI; this.anchor.set(0, 0.5); }
      else if (d === 'down') { this.rotation = Math.PI / 2; this.anchor.set(0, 0.5); }
      else if (d === 'up') { this.rotation = -Math.PI / 2; this.anchor.set(0, 0.5); }
      else { this.rotation = 0; this.anchor.set(0, 0.5); }
    }
    _createBeamBitmap() {
      // PERFORMANCE FIX: Use cached bitmap instead of creating new one
      this.bitmap = bitmapCache.getBitmap(this.settings);
      // Positioning reference: left edge at origin (anchor.x = 0)
      this.x = 0; this.y = 0;
      this.opacity = this.settings.opacity;
    }
    _createOverlays() {
      // Clean up existing overlays first
      if (this._arcSprite) {
        this.removeChild(this._arcSprite);
        this._arcSprite = null;
      }
      if (this._warnSprite) {
        this.removeChild(this._warnSprite);
        this._warnSprite = null;
      }

      // Electric arcs overlay - PERFORMANCE FIX: Create smaller bitmap
      if (this.settings.electricArcs) {
        const W = this.bitmap.width, H = this.bitmap.height;
        this._arcSprite = new Sprite(new Bitmap(W, H));
        this._arcSprite.anchor.set(this.anchor.x, this.anchor.y);
        this._arcSprite.blendMode = PIXI.BLEND_MODES.ADD;
        this._arcSprite.opacity = 0;
        this.addChild(this._arcSprite);
      }
      // Warning pulse overlay - PERFORMANCE FIX: Reuse bitmap if possible
      if (this.settings.warningPulse) {
        const W = this.bitmap.width, H = this.bitmap.height;
        this._warnSprite = new Sprite(new Bitmap(W, H));
        const ctx = this._warnSprite.bitmap.context;
        ctx.fillStyle = 'rgba(255,0,0,0.2)';
        ctx.fillRect(0, 0, W, H);
        this._warnSprite.anchor.set(this.anchor.x, this.anchor.y);
        this._warnSprite.blendMode = PIXI.BLEND_MODES.ADD;
        this._warnSprite.opacity = 0;
        this.addChild(this._warnSprite);
      }
    }
    _drawArcs() {
      if (!this._arcSprite) return;
      const b = this._arcSprite.bitmap;
      b.clear();
      const ctx = b.context;
      const { r, g, b: bb } = hexToRgb(this.settings.color);
      // Create 1-2 arcs spanning random segment of the length
      const arcs = 1 + (Math.random() < 0.5 ? 1 : 0);
      for (let a = 0; a < arcs; a++) {
        const sx = Math.floor(this.settings.glow ? this.settings.glowSize : 0) + Math.floor(Math.random() * (this.settings.length * 0.5));
        const ex = sx + Math.floor(this.settings.length * (0.3 + Math.random() * 0.4));
        const cy = b.height / 2;
        const segs = 4 + Math.floor(Math.random() * 4);
        ctx.lineCap = 'round';
        // white core
        ctx.strokeStyle = 'rgba(255,255,255,0.9)';
        ctx.lineWidth = 1.2;
        ctx.beginPath();
        for (let i = 0; i <= segs; i++) {
          const t = i / segs;
          const x = sx + (ex - sx) * t;
          const amp = (this.settings.width + 2) * 0.4;
          const y = cy + (Math.random() - 0.5) * amp;
          if (i === 0) ctx.moveTo(x, y); else ctx.lineTo(x, y);
        }
        ctx.stroke();
        // colored glow pass
        ctx.strokeStyle = `rgba(${r},${g},${bb},0.55)`;
        ctx.lineWidth = 2.2;
        ctx.stroke();
      }
      this._arcSprite.opacity = 200;
      if (b && b._setDirty) b._setDirty();
    }
    update() {
      if (this._destroyed) return false;
      super.update();
      this._time += 1;
      // Pulse
      if (this.settings.pulse) {
        const sp = DEFAULTS.pulseSpeed;
        const a = 0.85 + 0.15 * sinFast(this._time * sp);
        this.opacity = clampNum(Math.round(this._baseOpacity * a), 0, 255);
      } else {
        this.opacity = this._baseOpacity;
      }
      // Flicker (multiplicative occasional dips)
      if (this.settings.flicker && Math.random() < 0.12) {
        this.opacity = Math.round(this.opacity * (1 - DEFAULTS.flickerIntensity * Math.random()));
      }
      // Warning pulse overlay
      if (this._warnSprite) {
        const v = 0.5 + 0.5 * sinFast(this._time * 0.2);
        this._warnSprite.opacity = Math.round(255 * 0.35 * v);
      }
      // Electric arcs timing
      if (this._arcSprite) {
        this._arcTimer--;
        if (this._arcTimer <= 0) {
          this._drawArcs();
          this._arcTimer = DEFAULTS.arcFrequency + Math.floor(Math.random() * 8);
        } else {
          // fade out
          this._arcSprite.opacity = Math.max(0, this._arcSprite.opacity - 25);
        }
      }
      // Floaty offset perpendicular to beam
      if (this.settings.floaty) {
        const phase = this._floatPhase + this._time * DEFAULTS.floatSpeed;
        const off = DEFAULTS.floatAmp * sinFast(phase);
        // Apply perpendicular offset by using rotation +/- 90deg
        const nx = Math.cos(this.rotation + Math.PI / 2);
        const ny = Math.sin(this.rotation + Math.PI / 2);
        this._floatOffsetX = nx * off;
        this._floatOffsetY = ny * off;
      } else {
        this._floatOffsetX = 0; this._floatOffsetY = 0;
      }
      // Apply offset to children too
      if (this._arcSprite) { this._arcSprite.x = this._floatOffsetX; this._arcSprite.y = this._floatOffsetY; }
      if (this._warnSprite) { this._warnSprite.x = this._floatOffsetX; this._warnSprite.y = this._floatOffsetY; }
      return true;
    }
    prepareForPool() {
      // Clean up for reuse without destroying cached bitmap
      this._destroyed = false;
      this._time = 0;
      this.opacity = 255;
      this.x = 0;
      this.y = 0;
      this._floatOffsetX = 0;
      this._floatOffsetY = 0;

      // Clean up overlays (these are not cached)
      if (this._arcSprite) {
        this.removeChild(this._arcSprite);
        if (this._arcSprite.bitmap) this._arcSprite.bitmap.destroy();
        this._arcSprite = null;
      }
      if (this._warnSprite) {
        this.removeChild(this._warnSprite);
        if (this._warnSprite.bitmap) this._warnSprite.bitmap.destroy();
        this._warnSprite = null;
      }
    }

    destroy(options) {
      this._destroyed = true;
      // Don't destroy cached bitmap - it's shared!
      // Only destroy overlay bitmaps
      try { if (this._arcSprite && this._arcSprite.bitmap) this._arcSprite.bitmap.destroy(); } catch(_){}
      try { if (this._warnSprite && this._warnSprite.bitmap) this._warnSprite.bitmap.destroy(); } catch(_){}
      super.destroy(options);
    }
  }

  function hexToRgb(hex) {
    const s = String(hex || '').replace('#', '').trim();
    const r = parseInt(s.substring(0, 2), 16);
    const g = parseInt(s.substring(2, 4), 16);
    const b = parseInt(s.substring(4, 6), 16);
    if (Number.isNaN(r) || Number.isNaN(g) || Number.isNaN(b)) {
      // Fallback to white if parsing fails; inputs are validated upstream.
      return { r: 255, g: 255, b: 255 };
    }
    return { r, g, b };
  }

  // PERFORMANCE FIX: Bitmap Cache & Sprite Pool
  class LaserBitmapCache {
    constructor() {
      this.cache = new Map(); // key -> bitmap
      this.spritePool = []; // Reusable sprites
      this.maxPoolSize = 50;
    }

    getBitmapKey(settings) {
      // Create unique key for bitmap based on visual properties
      return `${settings.length}_${settings.width}_${settings.color}_${settings.glow}_${settings.glowSize}`;
    }

    getBitmap(settings) {
      const key = this.getBitmapKey(settings);
      if (this.cache.has(key)) {
        return this.cache.get(key);
      }

      // Create new bitmap and cache it
      const bitmap = this.createBitmap(settings);
      this.cache.set(key, bitmap);
      return bitmap;
    }

    createBitmap(settings) {
      const len = settings.length;
      const w = Math.max(1, settings.width);
      const glow = settings.glow ? settings.glowSize : 0;
      const H = Math.max(1, w + glow * 2);
      const W = Math.max(2, len + glow * 2);
      const bmp = new Bitmap(W, H);
      const ctx = bmp.context;
      const { r, g, b } = hexToRgb(settings.color);

      ctx.save();
      // Outer atmospheric glow
      if (glow > 0) {
        const gradOuter = ctx.createLinearGradient(0, 0, 0, H);
        gradOuter.addColorStop(0, `rgba(${r},${g},${b},0)`);
        gradOuter.addColorStop(0.5, `rgba(${r},${g},${b},0.18)`);
        gradOuter.addColorStop(1, `rgba(${r},${g},${b},0)`);
        ctx.fillStyle = gradOuter;
        ctx.fillRect(0, 0, W, H);
      }
      // Medium scattering
      if (glow > 6) {
        const pad = Math.max(0, (glow) * 0.5);
        const h2 = Math.max(1, w + pad);
        const y2 = (H - h2) / 2;
        const gradMed = ctx.createLinearGradient(0, y2, 0, y2 + h2);
        gradMed.addColorStop(0, `rgba(${r},${g},${b},0)`);
        gradMed.addColorStop(0.5, `rgba(${r},${g},${b},0.35)`);
        gradMed.addColorStop(1, `rgba(${r},${g},${b},0)`);
        ctx.fillStyle = gradMed;
        ctx.fillRect(0, y2, W, h2);
      }
      // Inner glow
      {
        const h3 = Math.max(1, w + 2);
        const y3 = (H - h3) / 2;
        const gradInner = ctx.createLinearGradient(0, y3, 0, y3 + h3);
        gradInner.addColorStop(0, `rgba(${r},${g},${b},0)`);
        gradInner.addColorStop(0.5, `rgba(${r},${g},${b},0.7)`);
        gradInner.addColorStop(1, `rgba(${r},${g},${b},0)`);
        ctx.fillStyle = gradInner;
        ctx.fillRect(0, y3, W, h3);
      }
      // Core beam
      {
        const h4 = Math.max(1, w);
        const y4 = (H - h4) / 2;
        ctx.fillStyle = `rgba(${r},${g},${b},0.95)`;
        ctx.fillRect(glow, y4, len, h4);
      }
      // White-hot center core
      {
        const h5 = Math.max(1, Math.round(Math.max(1, w * 0.35)));
        const y5 = (H - h5) / 2;
        ctx.fillStyle = `rgba(255,255,255,0.85)`;
        ctx.fillRect(glow, y5, len, h5);
      }
      ctx.restore();
      if (bmp && bmp._setDirty) bmp._setDirty();
      return bmp;
    }

    getSprite(settings) {
      // Try to reuse sprite from pool
      if (this.spritePool.length > 0) {
        const sprite = this.spritePool.pop();
        sprite.setupForReuse(settings);
        return sprite;
      }

      // Create new sprite if pool is empty
      return new LaserLine(settings);
    }

    returnSprite(sprite) {
      if (this.spritePool.length < this.maxPoolSize) {
        sprite.prepareForPool();
        this.spritePool.push(sprite);
      } else {
        sprite.destroy();
      }
    }

    clearCache() {
      this.cache.forEach(bitmap => bitmap.destroy());
      this.cache.clear();
      this.spritePool.forEach(sprite => sprite.destroy());
      this.spritePool.length = 0;
    }
  }

  // Global bitmap cache instance
  const bitmapCache = new LaserBitmapCache();

  class LaserManager {
    constructor(scene) {
      this.scene = scene;
      this.layer = new Sprite();
      // Attach to the map tilemap so the engine's z-sorting applies.
      // Characters have z=3 (normal) or z=5 (above). Lower tiles are <=2.
      // Use 2.9 to keep lasers below characters but above lower tiles.
      this.layer.z = 2.9;
      if (scene._spriteset) {
        const ss = scene._spriteset;
        const container = ss._tilemap || ss._effectsContainer || ss;
        container.addChild(this.layer);
      }
      this.eventLasers = new Map(); // eventId -> { settingsList, sprites[] }
      this._lastMapId = $gameMap.mapId();

      // Performance optimization properties
      this._scanThrottleTimer = 0;
      this._pendingScans = new Set(); // Track which events need scanning
      this._isAsyncScanning = false;
      this._scanQueue = [];

      // Micro-task batching for ultra-smooth performance
      this._deferredBatch = new Set();
      this._batchProcessingScheduled = false;

      // Web Worker for heavy parsing (if supported)
      this._initializeWebWorker();

      this.scanEventsForLasers();
    }
    terminate() {
      this.clearAll();
      if (this.layer && this.layer.parent) this.layer.parent.removeChild(this.layer);

      // Terminate web worker
      if (this._worker) {
        this._worker.terminate();
        this._worker = null;
      }

      // Clear bitmap cache on scene termination to prevent memory leaks
      bitmapCache.clearCache();
    }

    _initializeWebWorker() {
      // Create inline Web Worker for parsing laser notetags
      try {
        const workerCode = `
          // Web Worker code for parsing laser notetags
          function parseLaserNotetagsFromText(text) {
            const out = [];
            if (!text) return out;
            const re = /<laser\\s*:(.*?)>/gi;
            let m;
            while ((m = re.exec(text)) !== null) {
              const paramStr = (m[1] || '').trim();
              const params = parseLaserParams(paramStr);
              if (params) out.push(params);
            }
            return out;
          }

          function parseLaserParams(paramStr) {
            try {
              const getKV = (rx, mapFn) => {
                const mm = paramStr.match(rx);
                return mm ? mapFn(mm[1]) : undefined;
              };
              const direction = (getKV(/direction=([a-zA-Z]+)/i, s => s.toLowerCase()) || 'right');
              const length = Math.max(10, Math.min(1000, getKV(/length=(\\d+)/i, Number) ?? 150));
              const color = getKV(/color=#?([0-9a-fA-F]{6})/i, s => '#' + s) || '#FF0000';
              const width = Math.max(1, Math.min(20, getKV(/width=(\\d+)/i, Number) ?? 4));
              const opacity = Math.max(0, Math.min(255, getKV(/opacity=(\\d+)/i, Number) ?? 220));
              const pulse = /pulse=true/i.test(paramStr);
              const flicker = /flicker=true/i.test(paramStr);
              const glow = /glow=true/i.test(paramStr);
              const glowSize = Math.max(0, Math.min(50, getKV(/glowSize=(\\d+)/i, Number) ?? 12));
              const electricArcs = /electricArcs=true/i.test(paramStr);
              const warningPulse = /warningPulse=true/i.test(paramStr);
              const floaty = /float=true/i.test(paramStr);
              return { direction, length, color, width, opacity, pulse, flicker, glow, glowSize, electricArcs, warningPulse, floaty };
            } catch (e) {
              return null;
            }
          }

          self.onmessage = function(e) {
            const { eventData, eventId } = e.data;
            const noteText = eventData.note || '';
            const commentText = eventData.commentText || '';

            const tags = [
              ...parseLaserNotetagsFromText(noteText),
              ...parseLaserNotetagsFromText(commentText)
            ];

            self.postMessage({ eventId, tags });
          };
        `;

        const blob = new Blob([workerCode], { type: 'application/javascript' });
        this._worker = new Worker(URL.createObjectURL(blob));

        this._worker.onmessage = (e) => {
          const { eventId, tags } = e.data;
          this._handleWorkerResult(eventId, tags);
        };

        this._workerSupported = true;
      } catch (error) {
        console.warn('EventLaserLines: Web Worker not supported, falling back to main thread');
        this._workerSupported = false;
      }
    }
    clearAll() {
      this.eventLasers.forEach(entry => {
        if (entry && entry.sprites) {
          entry.sprites.forEach(s => {
            if (s) {
              this.layer.removeChild(s);
              bitmapCache.returnSprite(s); // Return to pool instead of destroying
            }
          });
        }
      });
      this.eventLasers.clear();
    }
    scanEventsForLasers() {
      this.clearAll();
      $gameMap.events().forEach((ev) => {
        if (!ev || !ev.event()) return;
        const noteText = ev.event().note || '';
        const commentText = eventActivePageComments(ev) || '';
        const tags = [
          ...parseLaserNotetagsFromText(noteText),
          ...parseLaserNotetagsFromText(commentText)
        ];
        if (tags.length > 0) {
          const sprites = tags.map(t => bitmapCache.getSprite(t)); // Use sprite pool
          this.eventLasers.set(ev.eventId(), { settingsList: tags, sprites, evId: ev.eventId() });
          // Add to layer
          sprites.forEach(s => this.layer.addChild(s));
        }
      });
    }

    // SOLUTION 2: Selective Event Scanning - Only scan specific event
    scanSingleEventForLasers(eventId) {
      const ev = $gameMap.event(eventId);
      if (!ev || !ev.event()) return;

      // Remove existing lasers for this event
      const existing = this.eventLasers.get(eventId);
      if (existing && existing.sprites) {
        existing.sprites.forEach(s => {
          this.layer.removeChild(s);
          bitmapCache.returnSprite(s); // Return to pool
        });
        this.eventLasers.delete(eventId);
      }

      // Scan for new lasers
      const noteText = ev.event().note || '';
      const commentText = eventActivePageComments(ev) || '';
      const tags = [
        ...parseLaserNotetagsFromText(noteText),
        ...parseLaserNotetagsFromText(commentText)
      ];

      if (tags.length > 0) {
        const sprites = tags.map(t => bitmapCache.getSprite(t)); // Use sprite pool
        this.eventLasers.set(eventId, { settingsList: tags, sprites, evId: eventId });
        sprites.forEach(s => this.layer.addChild(s));
      }
    }

    // SOLUTION 1: Throttled Full Scan with Delay
    scanEventsForLasersThrottled() {
      // Reset throttle timer
      this._scanThrottleTimer = 10; // 10 frame delay (~167ms at 60fps)
    }

    // SOLUTION 3: TRUE OFF-MAIN-THREAD Processing using requestIdleCallback + frame splitting
    scanEventsForLasersOffMainThread() {
      if (this._isAsyncScanning) return;

      this._isAsyncScanning = true;
      this.clearAll();

      const events = $gameMap.events();
      let currentIndex = 0;

      const processChunk = (deadline) => {
        // Process events while we have idle time (or at least 1ms remaining)
        while ((deadline.timeRemaining() > 1 || deadline.didTimeout) && currentIndex < events.length) {
          const ev = events[currentIndex];
          currentIndex++;

          if (!ev || !ev.event()) continue;

          // Do the heavy parsing work during idle time
          const noteText = ev.event().note || '';
          const commentText = eventActivePageComments(ev) || '';
          const tags = [
            ...parseLaserNotetagsFromText(noteText),
            ...parseLaserNotetagsFromText(commentText)
          ];

          if (tags.length > 0) {
            // Defer sprite creation to next frame to avoid blocking
            this._deferredSpriteCreation = this._deferredSpriteCreation || [];
            this._deferredSpriteCreation.push({
              eventId: ev.eventId(),
              tags: tags
            });
          }
        }

        // If more events to process, schedule next chunk
        if (currentIndex < events.length) {
          this._scheduleNextChunk(processChunk);
        } else {
          // All events processed, now create sprites in small batches
          this._createDeferredSprites();
        }
      };

      // Start processing
      this._scheduleNextChunk(processChunk);
    }

    _scheduleNextChunk(callback) {
      // Use requestIdleCallback if available, otherwise use setTimeout
      if (window.requestIdleCallback) {
        window.requestIdleCallback(callback, { timeout: 16 }); // Max 16ms wait
      } else {
        setTimeout(() => callback({ timeRemaining: () => 5, didTimeout: false }), 0);
      }
    }

    _createDeferredSprites() {
      if (!this._deferredSpriteCreation || this._deferredSpriteCreation.length === 0) {
        this._isAsyncScanning = false;
        return;
      }

      // Create sprites in small batches to avoid frame drops
      const batchSize = 2; // Only 2 sprites per frame
      const batch = this._deferredSpriteCreation.splice(0, batchSize);

      batch.forEach(({ eventId, tags }) => {
        const sprites = tags.map(t => bitmapCache.getSprite(t)); // Use sprite pool
        this.eventLasers.set(eventId, { settingsList: tags, sprites, evId: eventId });
        sprites.forEach(s => this.layer.addChild(s));
      });

      // Schedule next batch
      if (this._deferredSpriteCreation.length > 0) {
        setTimeout(() => this._createDeferredSprites(), 0);
      } else {
        this._isAsyncScanning = false;
        this._deferredSpriteCreation = null;
      }
    }
    update() {
      // Handle throttled scanning
      if (this._scanThrottleTimer > 0) {
        this._scanThrottleTimer--;
        if (this._scanThrottleTimer === 0) {
          // Execute delayed scan
          this.scanEventsForLasers();
        }
      }

      // Process pending selective scans
      if (this._pendingScans.size > 0 && this._scanThrottleTimer === 0) {
        const eventId = this._pendingScans.values().next().value;
        this._pendingScans.delete(eventId);
        this.scanSingleEventForLasers(eventId);
      }

      // Refresh if map changed
      if (this._lastMapId !== $gameMap.mapId()) {
        this._lastMapId = $gameMap.mapId();
        this._pendingScans.clear(); // Clear pending scans on map change
        this.scanEventsForLasersWithWorker(); // Use Web Worker for map changes (truly off-main-thread)
        return;
      }

      // Follow events and update
      this.eventLasers.forEach((entry) => {
        const ev = $gameMap.event(entry.evId);
        if (!ev) return;
        const spr = this.scene._spriteset && this.scene._spriteset.findTargetSprite ? this.scene._spriteset.findTargetSprite(ev) : null;
        if (!spr) return;
        const ox = spr.x, oy = spr.y;
        // Position all lasers at event origin, then apply float offsets inside sprite
        entry.sprites = entry.sprites.filter(s => {
          try {
            s.x = ox + (s._floatOffsetX || 0);
            s.y = oy + (s._floatOffsetY || 0);
            const alive = s.update();
            if (!alive) { this.layer.removeChild(s); bitmapCache.returnSprite(s); }
            return alive;
          } catch (e) {
            console.warn('EventLaserLines: sprite update error', e);
            try { this.layer.removeChild(s); bitmapCache.returnSprite(s); } catch(_){}
            return false;
          }
        });
      });
    }

    // Add method to queue selective event scanning
    queueEventScan(eventId) {
      this._pendingScans.add(eventId);
    }

    // SOLUTION 5: Completely deferred processing - NO synchronous work
    queueEventScanDeferred(eventId) {
      const debugStart = performance.now();
      console.log(`[LASER DEBUG] queueEventScanDeferred called for event ${eventId}`);

      // Add to batch instead of processing immediately
      this._deferredBatch.add(eventId);
      console.log(`[LASER DEBUG] Added event ${eventId} to batch. Batch size: ${this._deferredBatch.size}`);

      // Schedule batch processing if not already scheduled
      if (!this._batchProcessingScheduled) {
        this._batchProcessingScheduled = true;
        console.log(`[LASER DEBUG] Scheduling batch processing`);

        // Use multiple scheduling strategies for maximum smoothness
        if (window.requestIdleCallback) {
          window.requestIdleCallback(() => this._processDeferredBatch(), { timeout: 100 });
          console.log(`[LASER DEBUG] Used requestIdleCallback`);
        } else {
          setTimeout(() => this._processDeferredBatch(), 0);
          console.log(`[LASER DEBUG] Used setTimeout fallback`);
        }
      }

      const debugEnd = performance.now();
      console.log(`[LASER DEBUG] queueEventScanDeferred took: ${(debugEnd - debugStart).toFixed(2)}ms`);
    }

    _processDeferredBatch() {
      this._batchProcessingScheduled = false;

      if (this._deferredBatch.size === 0) return;

      // Convert to array and clear batch
      const eventIds = Array.from(this._deferredBatch);
      this._deferredBatch.clear();

      // Process in micro-batches to avoid blocking
      let currentIndex = 0;
      const microBatchSize = 1; // Process 1 event per micro-batch

      const processMicroBatch = (deadline) => {
        const startTime = performance.now();
        let processed = 0;

        while (currentIndex < eventIds.length && processed < microBatchSize) {
          // Check if we have time budget
          if (deadline && deadline.timeRemaining() < 1 && !deadline.didTimeout) {
            break;
          }

          const eventId = eventIds[currentIndex];
          try {
            this.scanSingleEventForLasers(eventId);
          } catch (error) {
            console.warn(`EventLaserLines: Error processing event ${eventId}:`, error);
          }

          currentIndex++;
          processed++;

          // Safety check - don't spend more than 2ms per micro-batch
          if (performance.now() - startTime > 2) {
            break;
          }
        }

        // Schedule next micro-batch if more events to process
        if (currentIndex < eventIds.length) {
          if (window.requestIdleCallback) {
            window.requestIdleCallback(processMicroBatch, { timeout: 16 });
          } else {
            setTimeout(() => processMicroBatch({ timeRemaining: () => 5, didTimeout: false }), 0);
          }
        }
      };

      // Start processing
      if (window.requestIdleCallback) {
        window.requestIdleCallback(processMicroBatch, { timeout: 16 });
      } else {
        setTimeout(() => processMicroBatch({ timeRemaining: () => 5, didTimeout: false }), 0);
      }
    }



    // SOLUTION 4: Web Worker-based scanning (TRUE off-main-thread)
    scanEventsForLasersWithWorker() {
      if (!this._workerSupported || this._isAsyncScanning) {
        // Fallback to off-main-thread approach
        return this.scanEventsForLasersOffMainThread();
      }

      this._isAsyncScanning = true;
      this.clearAll();

      const events = $gameMap.events();
      this._pendingWorkerResults = new Map();
      this._expectedWorkerResults = 0;

      events.forEach((ev) => {
        if (!ev || !ev.event()) return;

        this._expectedWorkerResults++;
        const eventData = {
          note: ev.event().note || '',
          commentText: eventActivePageComments(ev) || ''
        };

        // Send to worker for parsing
        this._worker.postMessage({ eventData, eventId: ev.eventId() });
      });

      // If no events to process, finish immediately
      if (this._expectedWorkerResults === 0) {
        this._isAsyncScanning = false;
      }
    }

    _handleWorkerResult(eventId, tags) {
      this._pendingWorkerResults.set(eventId, tags);
      this._expectedWorkerResults--;

      // Process results in small batches to avoid frame drops
      if (this._expectedWorkerResults === 0) {
        this._processBatchedWorkerResults();
      }
    }

    _processBatchedWorkerResults() {
      const results = Array.from(this._pendingWorkerResults.entries());
      let currentIndex = 0;

      const processBatch = () => {
        const batchSize = 3; // Process 3 results per frame
        const endIndex = Math.min(currentIndex + batchSize, results.length);

        for (let i = currentIndex; i < endIndex; i++) {
          const [eventId, tags] = results[i];
          if (tags.length > 0) {
            const sprites = tags.map(t => bitmapCache.getSprite(t)); // Use sprite pool
            this.eventLasers.set(eventId, { settingsList: tags, sprites, evId: eventId });
            sprites.forEach(s => this.layer.addChild(s));
          }
        }

        currentIndex = endIndex;

        if (currentIndex < results.length) {
          // Schedule next batch
          setTimeout(processBatch, 0);
        } else {
          // All done
          this._isAsyncScanning = false;
          this._pendingWorkerResults.clear();
        }
      };

      processBatch();
    }
  }

  // Scene_Map integration
  const _Scene_Map_createSpriteset = Scene_Map.prototype.createSpriteset;
  Scene_Map.prototype.createSpriteset = function() {
    _Scene_Map_createSpriteset.call(this);
    if (!this._laserManager) this._laserManager = new LaserManager(this);
  };
  const _Scene_Map_update = Scene_Map.prototype.update;
  Scene_Map.prototype.update = function() {
    _Scene_Map_update.call(this);
    if (this._laserManager) this._laserManager.update();
  };
  const _Scene_Map_terminate = Scene_Map.prototype.terminate;
  Scene_Map.prototype.terminate = function() {
    if (this._laserManager) { this._laserManager.terminate(); this._laserManager = null; }
    _Scene_Map_terminate.call(this);
  };

  // Re-scan on Game_Map refresh - Use async scanning to prevent freezes
  const _Game_Map_refresh = Game_Map.prototype.refresh;
  Game_Map.prototype.refresh = function() {
    _Game_Map_refresh.call(this);
    if (SceneManager._scene && SceneManager._scene._laserManager) {
      // Use throttled approach for map refresh
      SceneManager._scene._laserManager.scanEventsForLasersThrottled();
    }
  };

  // DEBUGGING: Re-scan when an event's active page changes
  const _Game_Event_setupPage = Game_Event.prototype.setupPage;
  Game_Event.prototype.setupPage = function() {
    const debugStart = performance.now();
    console.log(`[LASER DEBUG] Event ${this.eventId()} setupPage START`);

    _Game_Event_setupPage.call(this);
    const afterOriginal = performance.now();
    console.log(`[LASER DEBUG] Event ${this.eventId()} original setupPage took: ${(afterOriginal - debugStart).toFixed(2)}ms`);

    // DEBUGGING: Completely disable laser processing to test if freeze still happens
    const DISABLE_LASER_PROCESSING = true;

    if (!DISABLE_LASER_PROCESSING && SceneManager._scene && SceneManager._scene._laserManager) {
      const beforeLaser = performance.now();
      // SOLUTION 5: Completely deferred processing - ZERO synchronous work
      SceneManager._scene._laserManager.queueEventScanDeferred(this.eventId());
      const afterLaser = performance.now();
      console.log(`[LASER DEBUG] Event ${this.eventId()} laser queue took: ${(afterLaser - beforeLaser).toFixed(2)}ms`);
    } else {
      console.log(`[LASER DEBUG] Event ${this.eventId()} laser processing DISABLED for testing`);
    }

    const debugEnd = performance.now();
    console.log(`[LASER DEBUG] Event ${this.eventId()} setupPage TOTAL: ${(debugEnd - debugStart).toFixed(2)}ms`);
  };

  // DEBUGGING: Add global performance monitoring for Game_Event.setupPage
  console.log('[LASER DEBUG] EventLaserLines plugin loaded with debugging enabled');

  // Check what other plugins might be hooking setupPage
  console.log('[LASER DEBUG] Current Game_Event.setupPage function:', Game_Event.prototype.setupPage.toString().substring(0, 200));

  let setupPageCallCount = 0;

  window.laserDebugStats = {
    totalSetupPageCalls: 0,
    totalSetupPageTime: 0,
    maxSetupPageTime: 0,
    setupPageTimes: []
  };

  // SOLUTION: Global setupPage throttling to prevent mass simultaneous calls
  const setupPageQueue = [];
  let isProcessingSetupPageQueue = false;

  // Override to throttle ALL setupPage calls globally
  Game_Event.prototype.setupPage = function() {
    const globalStart = performance.now();
    setupPageCallCount++;

    console.log(`[LASER DEBUG] === GLOBAL setupPage call #${setupPageCallCount} for event ${this.eventId()} ===`);

    // Add to queue instead of processing immediately if we're in a batch
    if (setupPageCallCount > 1 && !isProcessingSetupPageQueue) {
      console.log(`[LASER DEBUG] Queueing setupPage for event ${this.eventId()} (batch detected)`);
      setupPageQueue.push({
        event: this,
        eventId: this.eventId(),
        queueTime: globalStart
      });

      // Start processing queue
      if (!isProcessingSetupPageQueue) {
        isProcessingSetupPageQueue = true;
        setTimeout(() => processSetupPageQueue(), 0);
      }
      return;
    }

    // Call our instrumented version
    _Game_Event_setupPage.call(this);

    const globalEnd = performance.now();
    const totalTime = globalEnd - globalStart;

    window.laserDebugStats.totalSetupPageCalls++;
    window.laserDebugStats.totalSetupPageTime += totalTime;
    window.laserDebugStats.maxSetupPageTime = Math.max(window.laserDebugStats.maxSetupPageTime, totalTime);
    window.laserDebugStats.setupPageTimes.push(totalTime);

    console.log(`[LASER DEBUG] === GLOBAL setupPage #${setupPageCallCount} TOTAL TIME: ${totalTime.toFixed(2)}ms ===`);

    if (totalTime > 10) {
      console.warn(`[LASER DEBUG] ⚠️ SLOW setupPage detected: ${totalTime.toFixed(2)}ms for event ${this.eventId()}`);
    }
  };

  function processSetupPageQueue() {
    const batchSize = 3; // Process 3 events per frame
    const batch = setupPageQueue.splice(0, batchSize);

    console.log(`[LASER DEBUG] Processing setupPage batch of ${batch.length} events`);

    batch.forEach(({ event, eventId, queueTime }) => {
      const processStart = performance.now();
      console.log(`[LASER DEBUG] Processing queued setupPage for event ${eventId} (queued for ${(processStart - queueTime).toFixed(2)}ms)`);

      try {
        _Game_Event_setupPage.call(event);
      } catch (error) {
        console.error(`[LASER DEBUG] Error in queued setupPage for event ${eventId}:`, error);
      }

      const processEnd = performance.now();
      console.log(`[LASER DEBUG] Queued setupPage for event ${eventId} took ${(processEnd - processStart).toFixed(2)}ms`);
    });

    // Continue processing if more in queue
    if (setupPageQueue.length > 0) {
      setTimeout(() => processSetupPageQueue(), 0);
    } else {
      isProcessingSetupPageQueue = false;
      console.log(`[LASER DEBUG] Finished processing all queued setupPage calls`);
    }
  }

})();

